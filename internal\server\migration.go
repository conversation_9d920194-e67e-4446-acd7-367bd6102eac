package server

import (
	"context"
	"daisy-server/internal/model"
	"daisy-server/pkg/log"
	"daisy-server/pkg/sid"
	"os"

	"github.com/lib/pq"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type MigrateServer struct {
	db  *gorm.DB
	log *log.Logger
}

func NewMigrateServer(db *gorm.DB, log *log.Logger) *MigrateServer {
	return &MigrateServer{
		db:  db,
		log: log,
	}
}
func (m *MigrateServer) Start(ctx context.Context) error {
	if err := m.db.AutoMigrate(
		&model.SysUser{},
		&model.SysRole{},
		&model.SysDept{},
		&model.SysMenu{},
		&model.SysLog{},
		&model.SysApi{},
		&model.SysDict{},
		&model.SysConfig{},
		&model.SysTenant{},
		&model.CmsMeta{},
		&model.CmsPost{},
		&model.BusinessCompany{},
		&model.BusinessProduct{},
		&model.BusinessTalent{},
		&model.BusinessExpert{},
		&model.BusinessKnowledge{},
		&model.BusinessApp{},
		&model.BusinessReport{},
		&model.BusinessJob{},
	); err != nil {
		m.log.Error("system migrate error", zap.Error(err))
		return err
	}
	m.log.Info("AutoMigrate success")

	// 创建初始数据
	if err := m.initData(); err != nil {
		m.log.Error("create default data error", zap.Error(err))
		return err
	}
	m.log.Info("Create default data success")

	os.Exit(0)
	return nil
}

func (m *MigrateServer) Stop(ctx context.Context) error {
	m.log.Info("AutoMigrate stop")
	return nil
}

func (m *MigrateServer) initData() error {
	// 创建默认角色
	role, err := m.createDefaultRole()
	if err != nil {
		return err
	}

	// 创建默认租户
	tenant, err := m.createDefaultTenant()
	if err != nil {
		return err
	}

	// 创建默认用户
	if err := m.createDefaultUser(role, tenant); err != nil {
		return err
	}

	return nil
}

// 创建默认角色
func (m *MigrateServer) createDefaultRole() (*model.SysRole, error) {
	// 检查是否已存在超级管理员角色
	var existingRole model.SysRole
	if err := m.db.Where("code = ?", "super").First(&existingRole).Error; err == nil {
		m.log.Info("Default role already exists, returning existing role", zap.String("role", existingRole.Name))
		return &existingRole, nil
	}

	role := &model.SysRole{
		Name:    "超级管理员",
		Code:    "super",
		Summary: "超级管理员，拥有所有权限",
		Status:  true,
	}

	if err := m.db.Create(role).Error; err != nil {
		return nil, err
	}

	m.log.Info("Default role created successfully", zap.String("role", role.Name))
	return role, nil
}

// 创建默认租户
func (m *MigrateServer) createDefaultTenant() (*model.SysTenant, error) {
	// 检查是否已存在默认租户
	var existingTenant model.SysTenant
	if err := m.db.Where("code = ?", "default").First(&existingTenant).Error; err == nil {
		m.log.Info("Default tenant already exists, returning existing tenant", zap.String("tenant", existingTenant.Name))
		return &existingTenant, nil
	}

	tenant := &model.SysTenant{
		Name:   "默认租户",
		Code:   "default",
		Status: true,
	}

	if err := m.db.Create(tenant).Error; err != nil {
		return nil, err
	}

	m.log.Info("Default tenant created successfully", zap.String("tenant", tenant.Name))
	return tenant, nil
}

// 创建默认用户
func (m *MigrateServer) createDefaultUser(role *model.SysRole, tenant *model.SysTenant) error {
	// 检查是否已存在超级管理员用户
	var count int64
	if err := m.db.Model(&model.SysUser{}).Where("username = ?", "admin").Count(&count).Error; err != nil {
		return err
	}

	// 如果已存在，则不创建
	if count > 0 {
		m.log.Info("Super admin user already exists, skip creation")
		return nil
	}

	// 对密码进行加密
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 生成用户ID
	userId, err := sid.NewSid().GenString()
	if err != nil {
		return err
	}

	// 创建默认管理员用户
	adminUser := &model.SysUser{
		UserId:   userId,
		Username: "admin",
		Password: string(hashedPassword),
		Nickname: "超级管理员",
		Email:    "<EMAIL>",
		Status:   true,
		RoleIds:  pq.Int64Array{int64(role.ID)},
		TenantId: tenant.ID,
	}

	if err := m.db.Create(adminUser).Error; err != nil {
		return err
	}

	m.log.Info("Default user created successfully", zap.String("username", adminUser.Username))
	return nil
}
