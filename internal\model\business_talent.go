package model

import (
	v1 "daisy-server/api/v1"

	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessTalent struct {
	gorm.Model
	Name    string                `gorm:"size:255; not null; index; comment:名称"`
	Gender  uint                  `gorm:"default:0; index; comment:性别"`
	Age     uint                  `gorm:"default:0; index; comment:年龄"`
	Edu     uint                  `gorm:"default:0; index; comment:学历"`
	Exp     uint                  `gorm:"default:0; index; comment:经验"`
	Type    uint                  `gorm:"default:0; index; comment:人才类型"`
	Skills  datatypes.JSON        `gorm:"type:jsonb; comment:技能"`
	Area    string                `gorm:"size:32; comment:地区"`
	Phone   string                `gorm:"size:20; comment:电话"`
	Email   string                `gorm:"size:64; comment:邮箱"`
	Avatar  string                `gorm:"size:255; comment:头像"`
	Address string                `gorm:"size:255; comment:地址"`
	Summary string                `gorm:"type:text; comment:人才简介"`
	Detail  string                `gorm:"type:text; comment:详情"`
	Party   bool                  `gorm:"default:false; index; comment:是否参与"`
	Files   []v1.UploadFileParams `gorm:"type:jsonb; serializer:json; comment:附件"`
	Order   int                   `gorm:"default:0; index; comment:排序"`
	Flag    pq.Int64Array         `gorm:"type:integer[]; comment:标志"`
	Status  bool                  `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessTalent) TableName() string {
	return "business_talent"
}
