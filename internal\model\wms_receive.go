package model

import "gorm.io/gorm"

// 入库单表，用于管理仓库的入库单信息
type WmsReceive struct {
	gorm.Model
	Code      string `gorm:"size:64; not null; index; comment:编号"`
	Type      uint   `gorm:"default:0; index; comment:类型"`
	PartnerId uint   `gorm:"default:0; index; comment:供应商ID"`
	RelatedNo string `gorm:"size:64; not null; index; comment:关联编号"`
	Summary   string `gorm:"type:text; comment:备注"`
	Status    uint   `gorm:"default:1; index; comment:状态 1草稿 2待审核 3入库中 4已完成 5已作废"`
	TenantId  uint   `gorm:"default:0; index; comment:租户ID"`
	CreatedBy uint   `gorm:"size:64; comment:创建人"`
	UpdatedBy uint   `gorm:"size:64; comment:操作人"`
}

func (m *WmsReceive) TableName() string {
	return "wms_receive"
}
